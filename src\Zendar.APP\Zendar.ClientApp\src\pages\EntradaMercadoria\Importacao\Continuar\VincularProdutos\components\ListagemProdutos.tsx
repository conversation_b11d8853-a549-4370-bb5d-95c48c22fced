import {
  Box,
  useMediaQuery,
  Text,
  Icon,
  HStack,
  Flex,
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
} from '@chakra-ui/react';
import { useInfiniteQuery, keepPreviousData } from '@tanstack/react-query';
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  ColumnDef,
} from '@tanstack/react-table';
import { useVirtualizer } from '@tanstack/react-virtual';
import { useMemo, useRef, useCallback, useEffect, useState } from 'react';
import { FiCheckCircle, FiChevronUp } from 'react-icons/fi';

import { DecimalMask } from 'helpers/format/fieldsMasks';

import api, { ResponseApi } from 'services/api';

import { usePadronizacaoContext } from 'store/Padronizacao/Padronizacao';

import { ProdutoOptionProps } from 'pages/EntradaMercadoria/EntradaManual/LancamentoProdutos/ModalAdicionarProduto/validationForm';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import { ActionsMenu } from 'components/update/Table/ActionsMenu';
import { LoadMoreRowsParams } from 'components/update/Table/VirtualizedInfiniteTable';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';

import {
  Produto,
  InformacoesRodape,
  EntradaMercadoriaStatusVinculoProduto,
  ProdutoPaginadoRetorno,
} from '../hooks/useProdutosVinculacao';
import { TextoTooltip } from '../TextoTooltip';

interface ListagemProdutosProps {
  produtos: Produto[];
  informacoesRodape: InformacoesRodape;
  isLoading?: boolean;
  handleToggleLinhaProduto: (index: number) => void;
  handleEditar: (index: number) => Promise<void>;
  handleVincularProduto: (
    index: number,
    produtoPendenteVariacoes?: ProdutoOptionProps
  ) => Promise<void>;
  modoTelaCheia?: boolean;
  entradaMercadoriaId: string | null;
}

export function ListagemProdutos({
  produtos,
  informacoesRodape,
  handleToggleLinhaProduto,
  handleEditar,
  handleVincularProduto,
  modoTelaCheia = false,
  entradaMercadoriaId,
}: ListagemProdutosProps) {
  const { casasDecimais } = usePadronizacaoContext();

  const queryFnProdutosVinculacao = useCallback(
    async ({
      pageParam = 1,
      id,
    }: {
      pageParam?: number;
      id: string | null;
    }) => {
      if (!id) {
        return {
          registros: [],
          totalItens: 0,
          totalProdutos: 0,
          valorTotal: 0,
          todosProdutosVinculados: false,
          bloquearAlteracao: false,
        };
      }
      const response = await api.get<void, ResponseApi<ProdutoPaginadoRetorno>>(
        ConstanteEnderecoWebservice.ENTRADA_MERCADORIA_LISTAR_ITENS_PAGINADOS_IMPORTACAO_XML,
        {
          params: {
            id: id,
            paginaAtual: pageParam,
            tamanhoPagina: 10,
            campoOrdenacao: 'NomeProduto',
            direcaoOrdenacao: 'ASC',
          },
        }
      );

      if (!response?.sucesso) {
        throw new Error('Erro ao carregar produtos');
      }

      return {
        ...response.dados,
        registros:
          response.dados.registros?.map((registro) => ({
            ...registro,
            isOpen: false,
          })) || [],
      };
    },
    []
  );

  const tableContainerRef = useRef<HTMLDivElement>(null);

  // const obterCorBackground = (produto: Produto): string => {
  //   const enumStatus = EntradaMercadoriaStatusVinculoProduto;
  //   const vinculado = produto.statusVinculo === enumStatus.VINCULADO;
  //   const naoVinculado = produto.statusVinculo === enumStatus.NAO_VINCULADO;

  //   if (vinculado) return 'teal.600';
  //   if (naoVinculado) return 'white';
  //   return 'aquamarine.100';
  // };

  const colunas = useMemo<ColumnDef<Produto, any>[]>(
    () => [
      {
        id: 'descricaoProdutoNota',
        header: 'Produto',
        cell: ({ row }) => {
          const produto = row.original;
          const index = row.index;

          const produtoEstaVinculado =
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.VINCULADO;
          const statusQuePodemMostrarDetalhes = [
            EntradaMercadoriaStatusVinculoProduto.VINCULADO,
            EntradaMercadoriaStatusVinculoProduto.PENDENTE_INFORMAR_VARIACOES,
          ];
          const podeMostrarDetalhes =
            statusQuePodemMostrarDetalhes.includes(produto.statusVinculo) ||
            !!produto.dadosAdicionais;

          return (
            <Box
              cursor={podeMostrarDetalhes ? 'pointer' : 'default'}
              userSelect="none"
              fontSize="14px"
              onClick={() => {
                if (podeMostrarDetalhes) handleToggleLinhaProduto(index);
              }}
              color={produtoEstaVinculado ? 'white' : 'inherit'}
            >
              <Button
                tabIndex={0}
                bg="transparent"
                p="4px"
                pb="0px"
                mr="6px"
                h="fit-content"
                borderRadius="6px"
                _focus={{ background: 'gray.100' }}
                minW="16px"
                opacity={podeMostrarDetalhes ? '1' : '0'}
                pointerEvents={podeMostrarDetalhes ? 'all' : 'none'}
              >
                <Icon
                  as={FiChevronUp}
                  mb="6px"
                  transform={produto.isOpen ? '' : 'rotate(180deg)'}
                  role="button"
                  transition="all 0.3s"
                />
              </Button>
              {produto.descricaoProdutoNota}
              {produto.isOpen && produto.dadosAdicionais && (
                <Flex
                  position="absolute"
                  w="97%"
                  flexDir="row"
                  align="center"
                  pl="30px"
                  mt="4px"
                  gap="4px"
                  fontSize="12px"
                  fontWeight="bold"
                >
                  <TextoTooltip
                    texto={produto.dadosAdicionais}
                    maxWidth="100%"
                  />
                </Flex>
              )}
            </Box>
          );
        },
      },
      {
        id: 'quantidade',
        header: 'Quantidade',
        cell: ({ row }) => {
          const produto = row.original;
          const produtoEstaVinculado =
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.VINCULADO;
          return (
            <Text
              fontSize="14px"
              color={produtoEstaVinculado ? 'white' : 'inherit'}
            >
              {DecimalMask(
                produto.quantidade,
                casasDecimais.casasDecimaisQuantidade
              )}
            </Text>
          );
        },
      },
      {
        id: 'valorUnitario',
        header: 'Valor unitário',
        cell: ({ row }) => {
          const produto = row.original;
          const produtoEstaVinculado =
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.VINCULADO;
          return (
            <Text
              fontSize="14px"
              textAlign="right"
              color={produtoEstaVinculado ? 'white' : 'inherit'}
            >
              {DecimalMask(
                produto.valorUnitario,
                casasDecimais.casasDecimaisValor
              )}
            </Text>
          );
        },
      },
      {
        id: 'valorTotal',
        header: 'Valor total',
        cell: ({ row }) => {
          const produto = row.original;
          const produtoEstaVinculado =
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.VINCULADO;
          return (
            <Text
              fontSize="14px"
              textAlign="right"
              color={produtoEstaVinculado ? 'white' : 'inherit'}
            >
              {DecimalMask(produto.valorTotal, 2, 2)}
            </Text>
          );
        },
      },
      {
        id: 'acoes',
        header: 'Ações',
        cell: ({ row }) => {
          const produto = row.original;
          const index = row.index;
          const produtoEstaVinculado =
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.VINCULADO;
          const produtoNaoEstaVinculado =
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.NAO_VINCULADO;

          const handleVincularClick = () => {
            if (produtoNaoEstaVinculado) {
              handleVincularProduto(index);
              return;
            }

            handleVincularProduto(index, {
              id: produto.produtoVinculado?.id || '',
              nome: produto.produtoVinculado?.nome || '',
              tipoProduto: produto.produtoVinculado?.tipoProduto || 2,
              volumeUnitario: produto.produtoVinculado?.volumeUnitario || false,
              referencia: produto.produtoVinculado?.referencia || '',
              precoCompra: produto.produtoVinculado?.precoCompra || 0,
              coresOptions: [],
              tamanhosOptions: [],
            });
          };

          if (produtoEstaVinculado) {
            return (
              <Flex justifyContent="space-between">
                <HStack spacing="1" color="secondary.300">
                  <Icon as={FiCheckCircle} boxSize="4" />
                  <Text fontSize="xs">Vinculado</Text>
                </HStack>
                <ActionsMenu
                  colorScheme="white"
                  backgroundHoverColor="gray.500"
                  menuZIndex="popover"
                  items={[
                    {
                      content: 'Editar',
                      onClick: () => handleEditar(index),
                    },
                  ]}
                />
              </Flex>
            );
          }

          return (
            <Flex alignItems="center" justifyContent="center">
              <Button
                size="xs"
                colorScheme="orange"
                minW="136px"
                onClick={handleVincularClick}
              >
                {produtoNaoEstaVinculado
                  ? 'Vincular ao sistema'
                  : 'Informar variações'}
              </Button>
            </Flex>
          );
        },
      },
    ],
    [
      handleToggleLinhaProduto,
      handleEditar,
      handleVincularProduto,
      casasDecimais,
    ]
  );

  const { data, fetchNextPage, isFetching, isLoading } =
    useInfiniteQuery<ProdutoPaginadoRetorno>({
      queryKey: ['produtos-vinculacao', entradaMercadoriaId],
      queryFn: ({ pageParam }) =>
        queryFnProdutosVinculacao({
          pageParam: (pageParam as number) || 1,
          entradaMercadoriaId: entradaMercadoriaId,
        }),
      initialPageParam: 1,
      getNextPageParam: (lastPage, allPages) => {
        const totalPaginas = Math.ceil(lastPage.totalItens / 10);
        const paginaAtual = allPages.length;
        return paginaAtual < totalPaginas ? paginaAtual + 1 : undefined;
      },
      enabled: !!entradaMercadoriaId,
      refetchOnWindowFocus: false,
    });

  const flatData = useMemo(
    () => data?.pages?.flatMap((page) => page.registros) ?? [],
    [data]
  );

  console.log('flatData', flatData);

  const totalDBRowCount = data?.pages?.[0]?.totalProdutos ?? 0;
  const totalFetched = flatData.length;

  const fetchMoreOnBottomReached = useCallback(
    (containerRefElement?: HTMLDivElement | null) => {
      if (containerRefElement) {
        const { scrollHeight, scrollTop, clientHeight } = containerRefElement;
        if (
          scrollHeight - scrollTop - clientHeight < 500 &&
          !isFetching &&
          totalFetched < totalDBRowCount
        ) {
          fetchNextPage();
        }
      }
    },
    [fetchNextPage, isFetching, totalFetched, totalDBRowCount]
  );

  useEffect(() => {
    fetchMoreOnBottomReached(tableContainerRef.current);
  }, [fetchMoreOnBottomReached]);

  const table = useReactTable({
    data: flatData,
    columns: colunas,
    getCoreRowModel: getCoreRowModel(),
  });

  const { rows } = table.getRowModel();

  const rowVirtualizer = useVirtualizer({
    count: rows?.length ?? 0,
    getScrollElement: () => tableContainerRef.current,
    estimateSize: () => 52, // altura estimada de cada linha
  });

  const virtualItems = rowVirtualizer.getVirtualItems();

  return (
    <Box
      position="relative"
      height="600px"
      bg="white"
      borderRadius="md"
      boxShadow="md"
    >
      {isLoading && produtos.length === 0 && <LoadingPadrao />}

      {/* Cabeçalho da tabela */}
      <Table variant="simple" size="sm">
        <Thead position="sticky" top={0} bg="white" zIndex={1}>
          {table.getHeaderGroups().map((headerGroup) => (
            <Tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <Th key={header.id} {...header.column.columnDef.meta}>
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                </Th>
              ))}
            </Tr>
          ))}
        </Thead>
      </Table>

      {/* Container virtualizado */}
      <Box
        ref={tableContainerRef}
        height="calc(100% - 60px)"
        onScroll={(event) => fetchMoreOnBottomReached(event.currentTarget)}
        overflow="auto"
        position="relative"
      >
        {/* <Box width="100%" position="relative"> */}
        {virtualItems.map((virtualItem) => {
          const row = table.getRowModel().rows[virtualItem.index];
          if (!row) return null;

          return (
            <Box
              key={virtualItem.key}
              position="absolute"
              top={0}
              left={0}
              width="100%"
              transform={`translateY(${virtualItem.start}px)`}
            >
              <Table variant="simple" size="sm">
                <Tbody>
                  <Tr>
                    {row.getVisibleCells().map((cell) => (
                      <Td key={cell.id} {...cell.column.columnDef.meta}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </Td>
                    ))}
                  </Tr>
                </Tbody>
              </Table>
            </Box>
          );
        })}
        {/* </Box> */}
      </Box>

      {/* Mensagem quando não há dados */}
      {produtos.length === 0 && !isLoading && (
        <Box p={6} textAlign="center">
          <Text color="gray.500">Nenhum produto encontrado</Text>
        </Box>
      )}

      {/* Loading no final da lista */}
      {isLoading && produtos.length > 0 && (
        <Box p={4} textAlign="center">
          <Text color="gray.500">Carregando mais produtos...</Text>
        </Box>
      )}
    </Box>
  );
}
